@extends('layouts.admin')

@section('title', 'Chi tiết Giftcode')

@section('content')
<div class="container-fluid">
    <div class="page-header">
        <h1 class="page-title">👁️ Chi tiết Giftcode</h1>
        <div class="page-actions">
            <a href="{{ route('admin.giftcode.edit', $giftcode->id) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Chỉnh sửa
            </a>
            <a href="{{ route('admin.giftcode.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Basic Information -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">📋 Thông tin cơ bản</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <label>ID:</label>
                                <span>{{ $giftcode->id }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label>Trạng thái:</label>
                                @if($giftcode->is_active)
                                    <span class="badge badge-success">Hoạt động</span>
                                @else
                                    <span class="badge badge-danger">Vô hiệu hóa</span>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <label>Tên:</label>
                                <span>{{ $giftcode->name ?: $giftcode->content }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label>Mã Code:</label>
                                @if(count($codes) > 1)
                                    <span class="code-display">{{ $codes[0] }} <small>(+{{ count($codes) - 1 }} khác)</small></span>
                                @else
                                    <span class="code-display">{{ $codes[0] ?? 'N/A' }}</span>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="info-item">
                                <label>Nội dung:</label>
                                <span>{{ $giftcode->content }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <label>Loại:</label>
                                <span>
                                    @switch($giftcode->type)
                                        @case(1)
                                            <span class="badge badge-primary">Công khai</span>
                                            @break
                                        @case(2)
                                            <span class="badge badge-warning">Riêng tư</span>
                                            @break
                                        @case(0)
                                            <span class="badge badge-info">Theo nhân vật</span>
                                            @break
                                        @default
                                            <span class="badge badge-secondary">Khác</span>
                                    @endswitch
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label>Server:</label>
                                <span>{{ $giftcode->zoneid == 0 ? 'Tất cả server' : 'Server ' . $giftcode->zoneid }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <label>Giới hạn sử dụng:</label>
                                <span>{{ $giftcode->limit > 0 ? number_format($giftcode->limit) : 'Không giới hạn' }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label>Đã sử dụng:</label>
                                <span>{{ number_format($giftcode->getUsageCount()) }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <label>Ngày tạo:</label>
                                <span>{{ \Carbon\Carbon::parse($giftcode->created_at)->format('d/m/Y H:i:s') }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label>Hết hạn:</label>
                                @if($giftcode->period > 0)
                                    <span>{{ \Carbon\Carbon::parse($giftcode->created_at)->addDays($giftcode->period)->format('d/m/Y H:i:s') }}</span>
                                @else
                                    <span>Không giới hạn</span>
                                @endif
                            </div>
                        </div>
                    </div>

                    @if($giftcode->accounts && $giftcode->type == 2)
                    <div class="row">
                        <div class="col-12">
                            <div class="info-item">
                                <label>Tài khoản được phép:</label>
                                <span>{{ $giftcode->accounts }}</span>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Items -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">🎁 Vật phẩm</h3>
                </div>
                <div class="card-body">
                    @if($giftcode->items && count($giftcode->getItemsArray()) > 0)
                        <div class="items-list">
                            @foreach($giftcode->getItemsArray() as $item)
                                @php
                                    $parts = explode(',', trim($item));
                                    if (count($parts) >= 7) {
                                        $itemId = $parts[0];
                                        $count = $parts[1];
                                        $binding = $parts[2];
                                        $forgeLevel = $parts[3];
                                        $appendPropLev = $parts[4];
                                        $lucky = $parts[5];
                                        $excellenceInfo = $parts[6];
                                    }
                                @endphp
                                @if(count($parts) >= 7)
                                    <div class="item-entry">
                                        <span class="item-name">Item ID: {{ $itemId }}</span>
                                        <span class="item-details">
                                            Số lượng: {{ $count }},
                                            Binding: {{ $binding }},
                                            Forge Level: {{ $forgeLevel }},
                                            Append Prop: {{ $appendPropLev }},
                                            Lucky: {{ $lucky }},
                                            Excellence: {{ $excellenceInfo }}
                                        </span>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    @else
                        <p class="text-muted">Không có vật phẩm nào</p>
                    @endif
                </div>
            </div>

            <!-- All Codes (if multiple) -->
            @if(count($codes) > 1)
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">🔢 Tất cả mã Code ({{ count($codes) }})</h3>
                </div>
                <div class="card-body">
                    <div class="codes-grid">
                        @foreach($codes as $index => $code)
                            <div class="code-item">
                                <span class="code-text">{{ $code }}</span>
                                <button class="btn btn-sm btn-outline-primary copy-btn" data-code="{{ $code }}">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Statistics -->
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">📊 Thống kê</h3>
                </div>
                <div class="card-body">
                    @php
                        $usageCount = $giftcode->getUsageCount();
                        $usagePercent = $giftcode->limit > 0 ? ($usageCount / $giftcode->limit) * 100 : 0;
                        $remainingUses = $giftcode->limit > 0 ? max(0, $giftcode->limit - $usageCount) : 'Không giới hạn';
                    @endphp

                    <div class="stat-item">
                        <div class="stat-label">Tỷ lệ sử dụng</div>
                        <div class="progress mb-2">
                            <div class="progress-bar" style="width: {{ min($usagePercent, 100) }}%">
                                {{ number_format($usagePercent, 1) }}%
                            </div>
                        </div>
                    </div>

                    <div class="stat-item">
                        <div class="stat-label">Còn lại</div>
                        <div class="stat-value">{{ is_numeric($remainingUses) ? number_format($remainingUses) : $remainingUses }}</div>
                    </div>

                    <div class="stat-item">
                        <div class="stat-label">Trạng thái</div>
                        <div class="stat-value">
                            @php
                                $isExpired = $giftcode->period > 0 && \Carbon\Carbon::parse($giftcode->created_at)->addDays($giftcode->period)->isPast();
                                $isUsedUp = $giftcode->limit > 0 && $usageCount >= $giftcode->limit;
                            @endphp

                            @if(!$giftcode->is_active)
                                <span class="badge badge-secondary">Vô hiệu hóa</span>
                            @elseif($isExpired)
                                <span class="badge badge-danger">Hết hạn</span>
                            @elseif($isUsedUp)
                                <span class="badge badge-warning">Hết lượt</span>
                            @else
                                <span class="badge badge-success">Hoạt động</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">⚡ Thao tác nhanh</h3>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="toggleStatus({{ $giftcode->id }})">
                            @if($giftcode->is_active)
                                <i class="fas fa-pause"></i> Vô hiệu hóa
                            @else
                                <i class="fas fa-play"></i> Kích hoạt
                            @endif
                        </button>
                        
                        <button class="btn btn-outline-danger" onclick="deleteGiftcode({{ $giftcode->id }})">
                            <i class="fas fa-trash"></i> Xóa
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Usage History -->
    @if($usageHistory && $usageHistory->count() > 0)
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">📈 Lịch sử sử dụng ({{ $usageHistory->count() }} gần nhất)</h3>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Thời gian</th>
                            <th>Tài khoản</th>
                            <th>Nhân vật</th>
                            <th>Server</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($usageHistory as $usage)
                            <tr>
                                <td>{{ \Carbon\Carbon::parse($usage->created_at)->format('d/m/Y H:i:s') }}</td>
                                <td>{{ $usage->uid }}</td>
                                <td>{{ $usage->rid ?: 'N/A' }}</td>
                                <td>{{ $usage->zoneid ?: 'N/A' }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    @endif
</div>

<script>
// Copy code functionality
document.querySelectorAll('.copy-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const code = this.dataset.code;
        navigator.clipboard.writeText(code).then(() => {
            this.innerHTML = '<i class="fas fa-check"></i>';
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-copy"></i>';
            }, 2000);
        });
    });
});

// Toggle status
function toggleStatus(id) {
    if (!confirm('Bạn có chắc chắn muốn thay đổi trạng thái giftcode này?')) {
        return;
    }

    fetch(`/admin/giftcode/${id}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Lỗi: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi thay đổi trạng thái');
    });
}

// Delete giftcode
function deleteGiftcode(id) {
    if (!confirm('Bạn có chắc chắn muốn xóa giftcode này? Hành động này không thể hoàn tác!')) {
        return;
    }

    fetch(`/admin/giftcode/${id}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            window.location.href = '{{ route("admin.giftcode.index") }}';
        } else {
            alert('Lỗi: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi xóa giftcode');
    });
}
</script>

<style>
.info-item {
    margin-bottom: 15px;
}

.info-item label {
    font-weight: bold;
    color: #495057;
    margin-right: 10px;
}

.code-display {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.reward-item {
    margin-bottom: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 5px;
}

.reward-section {
    margin-top: 15px;
}

.items-list {
    margin-top: 10px;
}

.item-entry {
    padding: 8px;
    margin-bottom: 5px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.item-name {
    font-weight: bold;
    color: #495057;
}

.item-details {
    font-size: 0.9em;
    color: #6c757d;
    margin-left: 10px;
}

.codes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
}

.code-item {
    display: flex;
    align-items: center;
    padding: 8px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.code-text {
    font-family: 'Courier New', monospace;
    flex: 1;
    margin-right: 10px;
}

.stat-item {
    margin-bottom: 20px;
}

.stat-label {
    font-size: 0.9em;
    color: #6c757d;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1.1em;
    font-weight: bold;
}

.progress {
    height: 20px;
}

.badge {
    font-size: 0.8em;
}
</style>
@endsection
